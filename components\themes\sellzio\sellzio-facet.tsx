import { useState, useEffect } from "react"
import { X } from "lucide-react"
import "./sellzio-styles.css"

interface FacetData {
  categories: Record<string, number>
  priceRanges: Record<string, number>
  ratings: Record<string, number>
  shipping: Record<string, number>
  features: Record<string, number>
}

interface ActiveFilters {
  categories?: string[]
  priceRanges?: string[]
  ratings?: string[]
  shipping?: string[]
  features?: string[]
}

interface SellzioFacetProps {
  searchResults: any[]
  activeFilters: ActiveFilters
  onFiltersChange: (filters: ActiveFilters) => void
  isVisible: boolean
  onClose: () => void
}

export function SellzioFacet({
  searchResults,
  activeFilters,
  onFiltersChange,
  isVisible,
  onClose
}: SellzioFacetProps) {
  const [tempFilters, setTempFilters] = useState<ActiveFilters>(activeFilters)
  const [facetData, setFacetData] = useState<FacetData>({
    categories: {},
    priceRanges: {},
    ratings: {},
    shipping: {},
    features: {}
  })
  const [isMobile, setIsMobile] = useState(false)

  // Check screen size
  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 1025)
    }

    checkScreenSize()
    window.addEventListener('resize', checkScreenSize)

    return () => window.removeEventListener('resize', checkScreenSize)
  }, [])

  // Extract facets from search results
  useEffect(() => {
    if (searchResults.length > 0) {
      const facets = extractFacets(searchResults)
      setFacetData(facets)
    }
  }, [searchResults])

  // Reset temp filters when activeFilters change
  useEffect(() => {
    setTempFilters(activeFilters)
  }, [activeFilters])

  const extractFacets = (results: any[]): FacetData => {
    // Jika tidak ada hasil, berikan data sample untuk demo
    if (results.length === 0) {
      return {
        categories: {
          "Handphone & Tablet": 45,
          "Elektronik": 32,
          "Fashion Pria": 28,
          "Fashion Wanita": 41,
          "Tas & Travel": 19,
          "Sepatu": 23,
          "Aksesoris Fashion": 15
        },
        priceRanges: {
          "Di bawah Rp 100.000": 67,
          "Rp 100.000 - Rp 500.000": 89,
          "Rp 500.000 - Rp 1.000.000": 45,
          "Rp 1.000.000 - Rp 5.000.000": 32,
          "Di atas Rp 5.000.000": 12
        },
        ratings: {
          "5 Bintang": 78,
          "4 Bintang ke atas": 156,
          "3 Bintang ke atas": 203
        },
        shipping: {
          "Gratis Ongkir": 134,
          "Same Day": 67,
          "Next Day": 89
        },
        features: {
          "COD": 98,
          "SellZio Mall": 56,
          "Flash Sale": 34
        }
      }
    }

    const facets: FacetData = {
      categories: {},
      priceRanges: {
        "Di bawah Rp 100.000": 0,
        "Rp 100.000 - Rp 500.000": 0,
        "Rp 500.000 - Rp 1.000.000": 0,
        "Rp 1.000.000 - Rp 5.000.000": 0,
        "Di atas Rp 5.000.000": 0
      },
      ratings: {
        "5 Bintang": 0,
        "4 Bintang ke atas": 0,
        "3 Bintang ke atas": 0
      },
      shipping: {
        "Gratis Ongkir": 0,
        "Same Day": 0,
        "Next Day": 0
      },
      features: {
        "COD": 0,
        "SellZio Mall": 0,
        "Flash Sale": 0
      }
    }

    results.forEach(product => {
      // Categories
      if (product.category) {
        facets.categories[product.category] = (facets.categories[product.category] || 0) + 1
      }

      // Price ranges
      const price = parseFloat(product.price?.replace(/[^\d]/g, '') || '0')
      if (price < 100000) {
        facets.priceRanges["Di bawah Rp 100.000"]++
      } else if (price < 500000) {
        facets.priceRanges["Rp 100.000 - Rp 500.000"]++
      } else if (price < 1000000) {
        facets.priceRanges["Rp 500.000 - Rp 1.000.000"]++
      } else if (price < 5000000) {
        facets.priceRanges["Rp 1.000.000 - Rp 5.000.000"]++
      } else {
        facets.priceRanges["Di atas Rp 5.000.000"]++
      }

      // Ratings
      const rating = product.rating || 0
      if (rating >= 5) facets.ratings["5 Bintang"]++
      if (rating >= 4) facets.ratings["4 Bintang ke atas"]++
      if (rating >= 3) facets.ratings["3 Bintang ke atas"]++

      // Shipping
      if (product.freeShipping) facets.shipping["Gratis Ongkir"]++
      if (product.sameDay) facets.shipping["Same Day"]++
      if (product.nextDay) facets.shipping["Next Day"]++

      // Features
      if (product.hasCod) facets.features["COD"]++
      if (product.isMall) facets.features["SellZio Mall"]++
      if (product.flashSale) facets.features["Flash Sale"]++
    })

    return facets
  }

  const handleFilterChange = (type: keyof ActiveFilters, value: string, checked: boolean) => {
    setTempFilters(prev => {
      const newFilters = { ...prev }
      if (!newFilters[type]) newFilters[type] = []

      if (checked) {
        if (!newFilters[type]!.includes(value)) {
          newFilters[type]!.push(value)
        }
      } else {
        newFilters[type] = newFilters[type]!.filter(item => item !== value)
        if (newFilters[type]!.length === 0) {
          delete newFilters[type]
        }
      }

      return newFilters
    })
  }

  // Function untuk menghapus filter individual dan langsung apply
  const removeFilter = (type: keyof ActiveFilters, value: string) => {
    const newFilters = { ...tempFilters }
    if (newFilters[type]) {
      newFilters[type] = newFilters[type]!.filter(item => item !== value)
      if (newFilters[type]!.length === 0) {
        delete newFilters[type]
      }
    }

    setTempFilters(newFilters)
    onFiltersChange(newFilters) // Langsung apply perubahan
  }

  const applyFilters = () => {
    onFiltersChange(tempFilters)
    onClose()
  }

  const resetFilters = () => {
    setTempFilters({})
    onFiltersChange({})
  }

  const countActiveFilters = () => {
    return Object.values(tempFilters).reduce((total, values) => total + (values?.length || 0), 0)
  }

  const renderFacetSection = (title: string, items: Record<string, number>, type: keyof ActiveFilters) => {
    const hasItems = Object.keys(items).some(key => items[key] > 0)
    if (!hasItems) return null

    return (
      <div className="facet-section">
        <h3>{title}</h3>
        <ul>
          {Object.entries(items).map(([key, count]) => {
            if (count === 0) return null
            const isChecked = tempFilters[type]?.includes(key) || false
            const checkboxId = `facet-${type}-${key.replace(/\s+/g, '-').replace(/[^a-z0-9-]/gi, '')}`

            return (
              <li key={key}>
                <input
                  type="checkbox"
                  id={checkboxId}
                  className="orange-checkbox"
                  checked={isChecked}
                  onChange={(e) => handleFilterChange(type, key, e.target.checked)}
                  data-facet-type={type}
                  data-facet-value={key}
                />
                <label htmlFor={checkboxId}>
                  {key} ({count})
                </label>
              </li>
            )
          })}
        </ul>
      </div>
    )
  }

  // Add active filters display component
  const renderActiveFilters = () => {
    const totalFilters = Object.values(tempFilters).reduce((total, values) => total + (values?.length || 0), 0)

    if (totalFilters === 0) return null

    return (
      <div className="active-filters">
        {Object.entries(tempFilters).map(([type, values]) =>
          values?.map((value: string) => (
            <div key={`${type}-${value}`} className="filter-tag">
              {value}
              <i
                className="fa fa-times"
                onClick={() => removeFilter(type as keyof ActiveFilters, value)}
              ></i>
            </div>
          ))
        )}
        {totalFilters > 1 && (
          <div
            className="filter-tag"
            style={{ backgroundColor: '#ffebe8', color: '#ee4d2d' }}
            onClick={resetFilters}
          >
            Reset Semua <i className="fa fa-times"></i>
          </div>
        )}
      </div>
    )
  }

  if (!isVisible) return null

  return (
    <>
      {/* Mobile/Tablet Overlay */}
      <div className="facet-overlay" style={{ display: isMobile ? 'flex' : 'none' }}>
        <div className="facet-panel">
          <div className="facet-header">
            <div className="facet-title">Filter</div>
            <div className="facet-close" onClick={onClose}>
              <X size={18} />
            </div>
          </div>

          <div className="facet-content-wrapper">
            <div className="facet-content">
              {renderActiveFilters()}
              {renderFacetSection('Kategori', facetData.categories, 'categories')}
              {renderFacetSection('Rentang Harga', facetData.priceRanges, 'priceRanges')}
              {renderFacetSection('Rating', facetData.ratings, 'ratings')}
              {renderFacetSection('Pengiriman', facetData.shipping, 'shipping')}
              {renderFacetSection('Fitur', facetData.features, 'features')}
            </div>
          </div>

          <div className="facet-buttons">
            <div className="facet-button facet-button-reset" onClick={resetFilters}>
              Reset
            </div>
            <div className="facet-button facet-button-apply" onClick={applyFilters}>
              Terapkan ({countActiveFilters()})
            </div>
          </div>
        </div>
      </div>

      {/* Desktop Panel */}
      <div
        className="facet-panel-desktop"
        style={{ display: !isMobile ? 'block' : 'none' }}
      >
        <div className="facet-header">
          <div className="facet-title">Filter</div>
          <div className="facet-close" onClick={onClose}>
            <X size={18} />
          </div>
        </div>

        <div className="facet-content-wrapper">
          <div className="facet-content">
            {renderActiveFilters()}
            {renderFacetSection('Kategori', facetData.categories, 'categories')}
            {renderFacetSection('Rentang Harga', facetData.priceRanges, 'priceRanges')}
            {renderFacetSection('Rating', facetData.ratings, 'ratings')}
            {renderFacetSection('Pengiriman', facetData.shipping, 'shipping')}
            {renderFacetSection('Fitur', facetData.features, 'features')}
          </div>
        </div>

        <div className="facet-buttons">
          <div className="facet-button facet-button-reset" onClick={resetFilters}>
            Reset
          </div>
          <div className="facet-button facet-button-apply" onClick={applyFilters}>
            Terapkan ({countActiveFilters()})
          </div>
        </div>
      </div>
    </>
  )
}